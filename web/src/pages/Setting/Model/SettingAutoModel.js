import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Space, 
  Switch, 
  Tag, 
  Popconfirm, 
  message, 
  InputNumber,
  Typography 
} from '@douyinfe/semi-ui';
import { 
  IconPlus, 
  IconEdit, 
  IconDelete,
  IconCheckCircleStroked,
  IconClose
} from '@douyinfe/semi-icons';
import { API, showError, showSuccess } from '../../../helpers';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;

export default function SettingAutoModel() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [configs, setConfigs] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [form] = Form.useForm();

  // Fetch all auto model configurations
  const fetchConfigs = async () => {
    setLoading(true);
    try {
      const res = await API.get('/api/auto_models');
      if (res.success) {
        setConfigs(res.data);
      } else {
        showError(res.message || t('获取配置失败'));
      }
    } catch (error) {
      showError(t('获取配置失败') + ': ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConfigs();
  }, []);

  // Handle form submission
  const handleSubmit = async (values) => {
    try {
      let res;
      if (editingConfig) {
        // Update existing config
        res = await API.put(`/api/auto_models/${editingConfig.name}`, values);
      } else {
        // Create new config
        res = await API.post('/api/auto_models', values);
      }
      
      if (res.success) {
        showSuccess(editingConfig ? t('更新成功') : t('创建成功'));
        setModalVisible(false);
        form.resetFields();
        fetchConfigs();
      } else {
        showError(res.message || (editingConfig ? t('更新失败') : t('创建失败')));
      }
    } catch (error) {
      showError(error.message);
    }
  };

  // Handle delete configuration
  const handleDelete = async (name) => {
    try {
      const res = await API.delete(`/api/auto_models/${name}`);
      if (res.success) {
        showSuccess(t('删除成功'));
        fetchConfigs();
      } else {
        showError(res.message || t('删除失败'));
      }
    } catch (error) {
      showError(error.message);
    }
  };

  // Open modal for adding/editing
  const openModal = (config = null) => {
    setEditingConfig(config);
    if (config) {
      form.setFieldsValue({
        name: config.name,
        description: config.description || '',
        is_active: config.is_active,
        models: config.models.join('\n')
      });
    } else {
      form.resetFields();
    }
    setModalVisible(true);
  };

  // Table columns
  const columns = [
    {
      title: t('名称'),
      dataIndex: 'name',
      key: 'name',
      render: (text) => <Tag color='blue'>{text}</Tag>,
    },
    {
      title: t('描述'),
      dataIndex: 'description',
      key: 'description',
      render: (text) => text || '-',
    },
    {
      title: t('模型列表'),
      dataIndex: 'models',
      key: 'models',
      render: (models) => (
        <Space wrap>
          {models.map((model, index) => (
            <Tag key={index}>{model}</Tag>
          ))}
        </Space>
      ),
    },
    {
      title: t('状态'),
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        active ? (
          <Tag color='green' icon={<IconCheckCircleStroked />}>{t('启用')}</Tag>
        ) : (
          <Tag color='red' icon={<IconClose />}>{t('禁用')}</Tag>
        )
      ),
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            icon={<IconEdit />} 
            onClick={() => openModal(record)}
          >
            {t('编辑')}
          </Button>
          <Popconfirm
            title={t('确认删除此配置吗？')}
            onConfirm={() => handleDelete(record.name)}
            okText={t('确认')}
            cancelText={t('取消')}
          >
            <Button icon={<IconDelete />} type="danger">
              {t('删除')}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button 
          type="primary" 
          icon={<IconPlus />} 
          onClick={() => openModal()}
        >
          {t('添加自动模型配置')}
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={configs}
        loading={loading}
        rowKey="name"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
      />

      <Modal
        title={editingConfig ? t('编辑自动模型配置') : t('添加自动模型配置')}
        visible={modalVisible}
        onOk={() => form.submit()}
        onCancel={() => setModalVisible(false)}
        width={600}
        okText={t('保存')}
        cancelText={t('取消')}
      >
        <Form 
          form={form} 
          onFinish={handleSubmit}
          labelPosition='left'
          labelWidth={100}
        >
          <Form.Input
            label={t('名称')}
            field="name"
            rules={[
              { required: true, message: t('请输入名称') },
              { 
                pattern: /^automodel-\w+$/, 
                message: t('名称必须以 automodel- 开头，且只能包含字母、数字和下划线') 
              }
            ]}
            disabled={!!editingConfig}
          />
          
          <Form.Input
            label={t('描述')}
            field="description"
          />
          
          <Form.Switch
            field="is_active"
            label={t('启用')}
            initValue={true}
          />
          
          <Form.TextArea
            label={t('模型列表')}
            field="models"
            placeholder={t('每行输入一个模型名称')}
            rules={[{ required: true, message: t('请至少输入一个模型') }]}
            style={{ minHeight: 150 }}
          />
          <Text type='tertiary'>{t('每行输入一个模型名称，按优先级从高到低排列')}</Text>
        </Form>
      </Modal>
    </div>
  );
}
