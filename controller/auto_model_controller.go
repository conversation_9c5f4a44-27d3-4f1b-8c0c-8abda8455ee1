package controller

import (
	"fmt"
	"net/http"
	"one-api/common"
	"one-api/model"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// CreateAutoModelConfigRequest defines the request body for creating/updating an auto model config
type CreateAutoModelConfigRequest struct {
	Name        string   `json:"name" binding:"required"`
	Description string   `json:"description"`
	IsActive    bool     `json:"is_active"`
	Models      []string `json:"models" binding:"required,min=1"`
}

// ListAutoModelConfigs GET /api/auto_models
// @Summary List all auto model configurations
// @Description Get a list of all auto model configurations with their mappings
// @Tags Auto Models
// @Produce json
// @Success 200 {object} gin.H "success: Whether the request was successful, data: List of auto model configurations"
// @Router /auto_models [get]
func ListAutoModelConfigs(c *gin.Context) {
	configs, err := model.GetAllAutoModelConfigs(common.DB)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to retrieve auto model configurations: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    configs,
	})
}

// CreateAutoModelConfig POST /api/auto_models
// @Summary Create a new auto model configuration
// @Description Create a new auto model configuration with the specified models
// @Tags Auto Models
// @Accept json
// @Produce json
// @Param config body CreateAutoModelConfigRequest true "Auto model configuration"
// @Success 200 {object} gin.H "success: Whether the request was successful, message: Status message"
// @Router /auto_models [post]
func CreateAutoModelConfig(c *gin.Context) {
	var req CreateAutoModelConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// Validate auto model name format
	if !strings.HasPrefix(req.Name, "automodel-") {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Auto model name must start with 'automodel-'",
		})
		return
	}

	err := model.CreateAutoModelConfig(common.DB, req.Name, req.Description, req.IsActive, req.Models)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create auto model configuration: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Auto model configuration created successfully",
	})
}

// GetAutoModelConfig GET /api/auto_models/:name
// @Summary Get a specific auto model configuration
// @Description Get details of a specific auto model configuration by name
// @Tags Auto Models
// @Produce json
// @Param name path string true "Auto model name"
// @Success 200 {object} gin.H "success: Whether the request was successful, data: Auto model configuration"
// @Router /auto_models/{name} [get]
func GetAutoModelConfig(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Auto model name is required",
		})
		return
	}

	config, err := model.GetAutoModelConfig(common.DB, name)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "Auto model configuration not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to get auto model configuration: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// UpdateAutoModelConfig PUT /api/auto_models/:name
// @Summary Update an existing auto model configuration
// @Description Update an existing auto model configuration with the specified models
// @Tags Auto Models
// @Accept json
// @Produce json
// @Param name path string true "Auto model name"
// @Param config body CreateAutoModelConfigRequest true "Auto model configuration"
// @Success 200 {object} gin.H "success: Whether the request was successful, message: Status message"
// @Router /auto_models/{name} [put]
func UpdateAutoModelConfig(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Auto model name is required",
		})
		return
	}

	var req CreateAutoModelConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request: " + err.Error(),
		})
		return
	}

	// Ensure the name in the path matches the name in the request body
	if req.Name != name {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Auto model name in path does not match name in request body",
		})
		return
	}

	err := model.UpdateAutoModelConfig(common.DB, req.Name, req.Description, req.IsActive, req.Models)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "Auto model configuration not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to update auto model configuration: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Auto model configuration updated successfully",
	})
}

// DeleteAutoModelConfig DELETE /api/auto_models/:name
// @Summary Delete an auto model configuration
// @Description Delete an auto model configuration by name
// @Tags Auto Models
// @Produce json
// @Param name path string true "Auto model name"
// @Success 200 {object} gin.H "success: Whether the request was successful, message: Status message"
// @Router /auto_models/{name} [delete]
func DeleteAutoModelConfig(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Auto model name is required",
		})
		return
	}

	err := model.DeleteAutoModelConfig(common.DB, name)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "Auto model configuration not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete auto model configuration: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Auto model configuration deleted successfully",
	})
}
