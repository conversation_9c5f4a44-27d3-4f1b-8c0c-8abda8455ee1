package controller

import (
	"net/http"
	"one-api/common"
	"one-api/model"

	"github.com/gin-gonic/gin"
)

// ListAutoModels GET /api/auto_models
func ListAutoModels(c *gin.Context) {
	autoModels, err := model.GetAllAutoModels(common.DB)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    autoModels,
	})
}

// UpdateAutoModels POST /api/auto_models
func UpdateAutoModels(c *gin.Context) {
	var request struct {
		Models []string `json:"models"`
	}

	if err := c.ShouldBind<PERSON>(&request); err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request format",
		})
		return
	}

	if len(request.Models) == 0 {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "At least one model is required",
		})
		return
	}

	// Update models in database
	err := model.UpdateAutoModels(common.DB, request.Models)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Auto models updated successfully",
	})
}
